Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler) for DMA1_Channel3_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel4_IRQHandler) for DMA1_Channel4_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler) for DMA1_Channel6_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler) for DMA1_Channel7_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to initial.o(i.Initial) for Initial
    main.o(i.main) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.main) refers to uart_app.o(i.my_printf) for my_printf
    main.o(i.main) refers to motor_function.o(i.Z_Motor_MoveToAngle) for Z_Motor_MoveToAngle
    main.o(i.main) refers to wit_c_sdk.o(.data) for Z_Angle
    main.o(i.main) refers to usart.o(.bss) for huart1
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM2_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM2_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler) refers to usart.o(.bss) for hdma_usart3_tx
    stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler) refers to usart.o(.bss) for hdma_usart3_rx
    stm32f1xx_it.o(i.DMA1_Channel4_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel4_IRQHandler) refers to usart.o(.bss) for hdma_usart1_tx
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler) refers to usart.o(.bss) for hdma_usart2_tx
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.TIM3_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM3_IRQHandler) refers to tim.o(.bss) for htim3
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to color.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to color.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_app.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to uart_app.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to uart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to memcpya.o(.text) for __aeabi_memcpy
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to strtok.o(.text) for strtok
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to atoi.o(.text) for atoi
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to uart_app.o(i.my_printf) for my_printf
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.data) for .data
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart2
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to wit_c_sdk.o(i.WitSerialDataIn) for WitSerialDataIn
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to wit_c_sdk.o(.data) for rx_byte
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart2
    uart_app.o(i.HAL_UART_TxCpltCallback) refers to wit_c_sdk.o(.data) for y901s_tx_busy
    uart_app.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_app.o(i.my_printf) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    color.o(i.Circle_Control) refers to dfltui.o(.text) for __aeabi_ui2d
    color.o(i.Circle_Control) refers to dmul.o(.text) for __aeabi_dmul
    color.o(i.Circle_Control) refers to ddiv.o(.text) for __aeabi_ddiv
    color.o(i.Circle_Control) refers to cos.o(i.cos) for cos
    color.o(i.Circle_Control) refers to dadd.o(.text) for __aeabi_dadd
    color.o(i.Circle_Control) refers to sin.o(i.sin) for sin
    color.o(i.Circle_Control) refers to cdcmple.o(.text) for __aeabi_cdcmple
    color.o(i.Circle_Control) refers to color.o(.data) for .data
    color.o(i.Circle_Control) refers to uart_app.o(.data) for Radius
    color.o(i.HAL_TIM_PeriodElapsedCallback) refers to color.o(i.center_point) for center_point
    color.o(i.HAL_TIM_PeriodElapsedCallback) refers to color.o(.data) for .data
    color.o(i.PID_Update) refers to fadd.o(.text) for __aeabi_fsub
    color.o(i.PID_Update) refers to cfcmple.o(.text) for __aeabi_cfcmple
    color.o(i.PID_Update) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    color.o(i.PID_Update) refers to fmul.o(.text) for __aeabi_fmul
    color.o(i.Star_Control) refers to dfltui.o(.text) for __aeabi_ui2d
    color.o(i.Star_Control) refers to dmul.o(.text) for __aeabi_dmul
    color.o(i.Star_Control) refers to ddiv.o(.text) for __aeabi_ddiv
    color.o(i.Star_Control) refers to dadd.o(.text) for __aeabi_dsub
    color.o(i.Star_Control) refers to cos.o(i.cos) for cos
    color.o(i.Star_Control) refers to sin.o(i.sin) for sin
    color.o(i.Star_Control) refers to cdcmple.o(.text) for __aeabi_cdcmple
    color.o(i.Star_Control) refers to color.o(.data) for .data
    color.o(i.Star_Control) refers to color.o(.constdata) for .constdata
    color.o(i.Star_Control) refers to uart_app.o(.data) for PX
    color.o(i.Throw_garbageX) refers to cdcmple.o(.text) for __aeabi_cdcmple
    color.o(i.Throw_garbageX) refers to dadd.o(.text) for __aeabi_dsub
    color.o(i.Throw_garbageX) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    color.o(i.Throw_garbageX) refers to color.o(.data) for .data
    color.o(i.Throw_garbageY) refers to cdcmple.o(.text) for __aeabi_cdcmple
    color.o(i.Throw_garbageY) refers to dadd.o(.text) for __aeabi_dsub
    color.o(i.Throw_garbageY) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    color.o(i.Throw_garbageY) refers to color.o(.data) for .data
    color.o(i.center_point) refers to uart_app.o(i.my_printf) for my_printf
    color.o(i.center_point) refers to dfltui.o(.text) for __aeabi_ui2d
    color.o(i.center_point) refers to dadd.o(.text) for __aeabi_drsub
    color.o(i.center_point) refers to color.o(i.Throw_garbageX) for Throw_garbageX
    color.o(i.center_point) refers to color.o(i.Throw_garbageY) for Throw_garbageY
    color.o(i.center_point) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    color.o(i.center_point) refers to d2f.o(.text) for __aeabi_d2f
    color.o(i.center_point) refers to color.o(i.PID_Update) for PID_Update
    color.o(i.center_point) refers to f2d.o(.text) for __aeabi_f2d
    color.o(i.center_point) refers to dmul.o(.text) for __aeabi_dmul
    color.o(i.center_point) refers to cdcmple.o(.text) for __aeabi_cdcmple
    color.o(i.center_point) refers to color.o(.data) for .data
    color.o(i.center_point) refers to uart_app.o(.data) for red_flag
    color.o(i.center_point) refers to usart.o(.bss) for huart2
    color.o(i.change_point) refers to fflti.o(.text) for __aeabi_i2f
    color.o(i.change_point) refers to fscalb.o(.text) for __ARM_scalbnf
    color.o(i.change_point) refers to ffltui.o(.text) for __aeabi_ui2f
    color.o(i.change_point) refers to fadd.o(.text) for __aeabi_fadd
    color.o(i.change_point) refers to f2d.o(.text) for __aeabi_f2d
    color.o(i.change_point) refers to dmul.o(.text) for __aeabi_dmul
    color.o(i.change_point) refers to dfltui.o(.text) for __aeabi_ui2d
    color.o(i.change_point) refers to dadd.o(.text) for __aeabi_drsub
    color.o(i.change_point) refers to cdcmple.o(.text) for __aeabi_cdcmple
    color.o(i.change_point) refers to color.o(.data) for .data
    color.o(i.change_point) refers to uart_app.o(.data) for Lx2
    yuntai.o(i.yuntai_ctrl) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    yuntai.o(i.yuntai_ctrl) refers to dadd.o(.text) for __aeabi_dadd
    yuntai.o(i.yuntai_ctrl) refers to ddiv.o(.text) for __aeabi_ddiv
    yuntai.o(i.yuntai_ctrl) refers to dmul.o(.text) for __aeabi_dmul
    yuntai.o(i.yuntai_ctrl) refers to dfixui.o(.text) for __aeabi_d2uiz
    yuntai.o(i.yuntai_ctrl) refers to cdcmple.o(.text) for __aeabi_cdcmple
    yuntai.o(i.yuntai_ctrl) refers to yuntai.o(.data) for .data
    yuntai.o(i.yuntai_ctrl) refers to color.o(.data) for Y_Angle
    yuntai.o(i.yuntai_init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    yuntai.o(i.yuntai_init) refers to dfixui.o(.text) for __aeabi_d2uiz
    yuntai.o(i.yuntai_init) refers to tim.o(.bss) for htim2
    yuntai.o(i.yuntai_init) refers to yuntai.o(.data) for .data
    initial.o(i.Initial) refers to wit_c_sdk.o(i.WitSdk_Init) for WitSdk_Init
    initial.o(i.Initial) refers to yuntai.o(i.yuntai_init) for yuntai_init
    initial.o(i.Initial) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    initial.o(i.Initial) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    initial.o(i.Initial) refers to motor_function.o(i.Motor_Init) for Motor_Init
    initial.o(i.Initial) refers to tim.o(.bss) for htim3
    initial.o(i.Initial) refers to uart_app.o(.bss) for uart_rx_dma_buffer
    initial.o(i.Initial) refers to usart.o(.bss) for huart1
    wit_c_sdk.o(i.CopeWitData) refers to memcpya.o(.text) for __aeabi_memcpy
    wit_c_sdk.o(i.CopeWitData) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.CopeWitData) refers to wit_c_sdk.o(.bss) for .bss
    wit_c_sdk.o(i.MyDataUpdateCallback) refers to fflti.o(.text) for __aeabi_i2f
    wit_c_sdk.o(i.MyDataUpdateCallback) refers to fscalb.o(.text) for __ARM_scalbnf
    wit_c_sdk.o(i.MyDataUpdateCallback) refers to fmul.o(.text) for __aeabi_fmul
    wit_c_sdk.o(i.MyDataUpdateCallback) refers to fadd.o(.text) for __aeabi_fsub
    wit_c_sdk.o(i.MyDataUpdateCallback) refers to wit_c_sdk.o(.bss) for .bss
    wit_c_sdk.o(i.MyDataUpdateCallback) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.STM32_Delayms) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    wit_c_sdk.o(i.STM32_SerialWrite) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT) for HAL_UART_Transmit_IT
    wit_c_sdk.o(i.STM32_SerialWrite) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.STM32_SerialWrite) refers to usart.o(.bss) for huart2
    wit_c_sdk.o(i.USART3_Start_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    wit_c_sdk.o(i.USART3_Start_Receive_IT) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.USART3_Start_Receive_IT) refers to usart.o(.bss) for huart2
    wit_c_sdk.o(i.WitCaliRefAngle) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitCaliRefAngle) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitCanDataIn) refers to wit_c_sdk.o(i.CopeWitData) for CopeWitData
    wit_c_sdk.o(i.WitCanDataIn) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitCanDataIn) refers to wit_c_sdk.o(.bss) for .bss
    wit_c_sdk.o(i.WitCanWriteRegister) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitDeInit) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitDelayMsRegister) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitI2cFuncRegister) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitInit) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitReadReg) refers to wit_c_sdk.o(i.__CRC16) for __CRC16
    wit_c_sdk.o(i.WitReadReg) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitReadReg) refers to wit_c_sdk.o(.bss) for .bss
    wit_c_sdk.o(i.WitRegisterCallBack) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSaveParameter) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSaveParameter) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSdk_Init) refers to wit_c_sdk.o(i.WitSerialWriteRegister) for WitSerialWriteRegister
    wit_c_sdk.o(i.WitSdk_Init) refers to wit_c_sdk.o(i.WitDelayMsRegister) for WitDelayMsRegister
    wit_c_sdk.o(i.WitSdk_Init) refers to wit_c_sdk.o(i.WitRegisterCallBack) for WitRegisterCallBack
    wit_c_sdk.o(i.WitSdk_Init) refers to wit_c_sdk.o(i.WitInit) for WitInit
    wit_c_sdk.o(i.WitSdk_Init) refers to wit_c_sdk.o(i.USART3_Start_Receive_IT) for USART3_Start_Receive_IT
    wit_c_sdk.o(i.WitSdk_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    wit_c_sdk.o(i.WitSdk_Init) refers to wit_c_sdk.o(i.WitCaliRefAngle) for WitCaliRefAngle
    wit_c_sdk.o(i.WitSdk_Init) refers to wit_c_sdk.o(i.STM32_SerialWrite) for STM32_SerialWrite
    wit_c_sdk.o(i.WitSdk_Init) refers to wit_c_sdk.o(i.STM32_Delayms) for STM32_Delayms
    wit_c_sdk.o(i.WitSdk_Init) refers to wit_c_sdk.o(i.MyDataUpdateCallback) for MyDataUpdateCallback
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(i.CopeWitData) for CopeWitData
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(i.__CRC16) for __CRC16
    wit_c_sdk.o(i.WitSerialDataIn) refers to memcpya.o(.text) for __aeabi_memcpy
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(.bss) for .bss
    wit_c_sdk.o(i.WitSerialWriteRegister) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetBandwidth) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetBandwidth) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetBandwidth) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetCanBaud) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetCanBaud) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetCanBaud) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetContent) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetContent) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetContent) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetForReset) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetForReset) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetOutputRate) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetOutputRate) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetOutputRate) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetUartBaud) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetUartBaud) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetUartBaud) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitStartAccCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStartAccCali) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitStartMagCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStartMagCali) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitStopAccCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStopAccCali) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitStopMagCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStopMagCali) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitWriteReg) refers to wit_c_sdk.o(i.__CRC16) for __CRC16
    wit_c_sdk.o(i.WitWriteReg) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.__CRC16) refers to wit_c_sdk.o(.constdata) for .constdata
    emm_v5.o(i.Emm_V5_En_Control) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_En_Control) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Pos_Control) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Pos_Control) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Stop_Now) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Stop_Now) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to usart.o(.bss) for huart3
    emm_v5.o(i.Emm_V5_Vel_Control) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Vel_Control) refers to usart.o(.bss) for huart3
    motor_function.o(i.Motor_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    motor_function.o(i.Motor_Init) refers to emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) for Emm_V5_Modify_Ctrl_Mode
    motor_function.o(i.Motor_Init) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    motor_function.o(i.Motor_Init) refers to motor_function.o(.data) for .data
    motor_function.o(i.Z_Motor_GetCurrentAngle) refers to motor_function.o(.data) for .data
    motor_function.o(i.Z_Motor_MoveToAngle) refers to fadd.o(.text) for __aeabi_fsub
    motor_function.o(i.Z_Motor_MoveToAngle) refers to f2d.o(.text) for __aeabi_f2d
    motor_function.o(i.Z_Motor_MoveToAngle) refers to dmul.o(.text) for __aeabi_dmul
    motor_function.o(i.Z_Motor_MoveToAngle) refers to dfixui.o(.text) for __aeabi_d2uiz
    motor_function.o(i.Z_Motor_MoveToAngle) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    motor_function.o(i.Z_Motor_MoveToAngle) refers to motor_function.o(.data) for .data
    motor_function.o(i.Z_Motor_RelativeMove) refers to fadd.o(.text) for __aeabi_fadd
    motor_function.o(i.Z_Motor_RelativeMove) refers to motor_function.o(i.Z_Motor_MoveToAngle) for Z_Motor_MoveToAngle
    motor_function.o(i.Z_Motor_RelativeMove) refers to motor_function.o(.data) for .data
    motor_function.o(i.Z_Motor_ResetAngle) refers to motor_function.o(.data) for .data
    motor_function.o(i.Z_Motor_Stop) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    cos.o(i.__softfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.cos) for cos
    cos.o(i.cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.cos) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.__cos$lsc) for __cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cos_x.o(i.__cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.__cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.__cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.__cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__softfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.sin) for sin
    sin.o(i.sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.sin) refers to errno.o(i.__set_errno) for __set_errno
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.__sin$lsc) for __sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sin_x.o(i.__sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.__sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.__sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.__sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fscalb.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfixi.o(.text) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(.text) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to dadd.o(.text) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to dscalb.o(.text) for __ARM_scalbn
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to dadd.o(.text) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(.text) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfixi.o(.text) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflti.o(.text) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to dfltui.o(.text) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to dscalb.o(.text) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(.text) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to dscalb.o(.text) for __ARM_scalbn
    sin_i.o(i.__kernel_sin) refers to dadd.o(.text) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    sin_i_x.o(i.____kernel_sin$lsc) refers to dadd.o(.text) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f103xb.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (36 bytes).
    Removing tim.o(i.HAL_TIM_PWM_MspDeInit), (24 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (160 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (152 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (184 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (416 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (228 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (156 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (104 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (128 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (184 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (140 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin), (10 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit), (160 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (114 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing uart_app.o(.data), (2 bytes).
    Removing uart_app.o(.data), (2 bytes).
    Removing uart_app.o(.data), (2 bytes).
    Removing uart_app.o(.data), (2 bytes).
    Removing uart_app.o(.data), (2 bytes).
    Removing color.o(.rev16_text), (4 bytes).
    Removing color.o(.revsh_text), (4 bytes).
    Removing color.o(.rrx_text), (6 bytes).
    Removing color.o(i.Circle_Control), (324 bytes).
    Removing color.o(i.Star_Control), (296 bytes).
    Removing color.o(i.change_point), (584 bytes).
    Removing color.o(.constdata), (6 bytes).
    Removing color.o(.data), (8 bytes).
    Removing color.o(.data), (8 bytes).
    Removing color.o(.data), (8 bytes).
    Removing color.o(.data), (8 bytes).
    Removing color.o(.data), (8 bytes).
    Removing color.o(.data), (8 bytes).
    Removing color.o(.data), (8 bytes).
    Removing color.o(.data), (8 bytes).
    Removing color.o(.data), (1 bytes).
    Removing yuntai.o(.rev16_text), (4 bytes).
    Removing yuntai.o(.revsh_text), (4 bytes).
    Removing yuntai.o(.rrx_text), (6 bytes).
    Removing yuntai.o(i.yuntai_ctrl), (188 bytes).
    Removing yuntai.o(.data), (8 bytes).
    Removing initial.o(.rev16_text), (4 bytes).
    Removing initial.o(.revsh_text), (4 bytes).
    Removing initial.o(.rrx_text), (6 bytes).
    Removing wit_c_sdk.o(.rev16_text), (4 bytes).
    Removing wit_c_sdk.o(.revsh_text), (4 bytes).
    Removing wit_c_sdk.o(.rrx_text), (6 bytes).
    Removing wit_c_sdk.o(i.CheckRange), (16 bytes).
    Removing wit_c_sdk.o(i.WitCanDataIn), (140 bytes).
    Removing wit_c_sdk.o(i.WitCanWriteRegister), (20 bytes).
    Removing wit_c_sdk.o(i.WitDeInit), (32 bytes).
    Removing wit_c_sdk.o(i.WitI2cFuncRegister), (28 bytes).
    Removing wit_c_sdk.o(i.WitReadReg), (312 bytes).
    Removing wit_c_sdk.o(i.WitSaveParameter), (68 bytes).
    Removing wit_c_sdk.o(i.WitSetBandwidth), (68 bytes).
    Removing wit_c_sdk.o(i.WitSetCanBaud), (88 bytes).
    Removing wit_c_sdk.o(i.WitSetContent), (84 bytes).
    Removing wit_c_sdk.o(i.WitSetForReset), (68 bytes).
    Removing wit_c_sdk.o(i.WitSetOutputRate), (84 bytes).
    Removing wit_c_sdk.o(i.WitSetUartBaud), (136 bytes).
    Removing wit_c_sdk.o(i.WitStartAccCali), (96 bytes).
    Removing wit_c_sdk.o(i.WitStartMagCali), (68 bytes).
    Removing wit_c_sdk.o(i.WitStopAccCali), (68 bytes).
    Removing wit_c_sdk.o(i.WitStopMagCali), (68 bytes).
    Removing emm_v5.o(.rev16_text), (4 bytes).
    Removing emm_v5.o(.revsh_text), (4 bytes).
    Removing emm_v5.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (160 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Sys_Params), (144 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Stop_Now), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Vel_Control), (68 bytes).
    Removing motor_function.o(.rev16_text), (4 bytes).
    Removing motor_function.o(.revsh_text), (4 bytes).
    Removing motor_function.o(.rrx_text), (6 bytes).
    Removing motor_function.o(i.Z_Motor_GetCurrentAngle), (12 bytes).
    Removing motor_function.o(i.Z_Motor_RelativeMove), (24 bytes).
    Removing motor_function.o(i.Z_Motor_ResetAngle), (12 bytes).
    Removing motor_function.o(i.Z_Motor_Stop), (8 bytes).
    Removing ffltui.o(.text), (10 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfixi.o(.text), (62 bytes).

435 unused section(s) (total 27629 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  fscalb.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ..\Cloud\color.c                         0x00000000   Number         0  color.o ABSOLUTE
    ..\Cloud\yuntai.c                        0x00000000   Number         0  yuntai.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\Initial\Initial.c                     0x00000000   Number         0  initial.o ABSOLUTE
    ..\JY901\wit_c_sdk.c                     0x00000000   Number         0  wit_c_sdk.o ABSOLUTE
    ..\K230\uart_app.c                       0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\Motor\Emm_V5.c                        0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\Motor\motor_function.c                0x00000000   Number         0  motor_function.o ABSOLUTE
    ..\\Cloud\\color.c                       0x00000000   Number         0  color.o ABSOLUTE
    ..\\Cloud\\yuntai.c                      0x00000000   Number         0  yuntai.o ABSOLUTE
    ..\\Initial\\Initial.c                   0x00000000   Number         0  initial.o ABSOLUTE
    ..\\JY901\\wit_c_sdk.c                   0x00000000   Number         0  wit_c_sdk.o ABSOLUTE
    ..\\K230\\uart_app.c                     0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\Motor\\Emm_V5.c                      0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\\Motor\\motor_function.c              0x00000000   Number         0  motor_function.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cfcmple.s                                0x00000000   Number         0  cfcmple.o ABSOLUTE
    cfrcmple.s                               0x00000000   Number         0  cfrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080000fc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080000fc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080000fc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080000fc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000100   Section       36  startup_stm32f103xb.o(.text)
    .text                                    0x08000124   Section        0  llushr.o(.text)
    .text                                    0x08000144   Section        0  memcpya.o(.text)
    .text                                    0x08000168   Section        0  memseta.o(.text)
    .text                                    0x0800018c   Section        0  strtok.o(.text)
    .text                                    0x080001d0   Section        0  atoi.o(.text)
    .text                                    0x080001ea   Section        0  fadd.o(.text)
    .text                                    0x0800029a   Section        0  fmul.o(.text)
    .text                                    0x080002fe   Section        0  fscalb.o(.text)
    .text                                    0x08000316   Section        0  dadd.o(.text)
    .text                                    0x08000464   Section        0  dmul.o(.text)
    .text                                    0x08000548   Section        0  ddiv.o(.text)
    .text                                    0x08000626   Section        0  fflti.o(.text)
    .text                                    0x08000638   Section        0  dfltui.o(.text)
    .text                                    0x08000652   Section        0  dfixui.o(.text)
    .text                                    0x08000684   Section        0  f2d.o(.text)
    .text                                    0x080006ac   Section       48  cdcmple.o(.text)
    .text                                    0x080006dc   Section       48  cdrcmple.o(.text)
    .text                                    0x0800070c   Section        0  d2f.o(.text)
    .text                                    0x08000744   Section       20  cfcmple.o(.text)
    .text                                    0x08000758   Section       20  cfrcmple.o(.text)
    .text                                    0x0800076c   Section        0  uidiv.o(.text)
    .text                                    0x08000798   Section        0  uldiv.o(.text)
    .text                                    0x080007fa   Section        0  llshl.o(.text)
    .text                                    0x08000818   Section        0  llsshr.o(.text)
    .text                                    0x0800083c   Section        0  strtol.o(.text)
    .text                                    0x080008ac   Section        0  fepilogue.o(.text)
    .text                                    0x080008ac   Section        0  iusefp.o(.text)
    .text                                    0x0800091a   Section        0  depilogue.o(.text)
    .text                                    0x080009d4   Section        0  dfixul.o(.text)
    .text                                    0x08000a04   Section       36  init.o(.text)
    .text                                    0x08000a28   Section        0  ctype_o.o(.text)
    .text                                    0x08000a30   Section        0  _strtoul.o(.text)
    .text                                    0x08000ace   Section        0  _chval.o(.text)
    .text                                    0x08000aea   Section        0  __dczerorl2.o(.text)
    i.BusFault_Handler                       0x08000b40   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.CopeWitData                            0x08000b44   Section        0  wit_c_sdk.o(i.CopeWitData)
    CopeWitData                              0x08000b45   Thumb Code   172  wit_c_sdk.o(i.CopeWitData)
    i.DMA1_Channel2_IRQHandler               0x08000bf8   Section        0  stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler)
    i.DMA1_Channel3_IRQHandler               0x08000c04   Section        0  stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler)
    i.DMA1_Channel4_IRQHandler               0x08000c10   Section        0  stm32f1xx_it.o(i.DMA1_Channel4_IRQHandler)
    i.DMA1_Channel5_IRQHandler               0x08000c1c   Section        0  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    i.DMA1_Channel6_IRQHandler               0x08000c28   Section        0  stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler)
    i.DMA1_Channel7_IRQHandler               0x08000c34   Section        0  stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler)
    i.DMA_SetConfig                          0x08000c40   Section        0  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08000c41   Thumb Code    42  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08000c6a   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Emm_V5_En_Control                      0x08000c6c   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_Modify_Ctrl_Mode                0x08000ca8   Section        0  emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode)
    i.Emm_V5_Pos_Control                     0x08000ce4   Section        0  emm_v5.o(i.Emm_V5_Pos_Control)
    i.Error_Handler                          0x08000d4c   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08000d50   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000d98   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08000e30   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08000f84   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08000fe0   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08001050   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08001074   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GetTick                            0x08001254   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08001260   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001270   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001294   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080012d4   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001310   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x0800132c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x0800136c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001390   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080014bc   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080014dc   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080014fc   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001548   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08001868   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08001890   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08001892   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08001894   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080018fc   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08001958   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08001994   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x080019ec   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_IC_CaptureCallback             0x08001ac8   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08001aca   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08001bfc   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08001c44   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08001c46   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08001d12   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08001d6c   Section        0  tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08001d90   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08001d94   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08001e30   Section        0  color.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08001e58   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08001e5a   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08001ea4   Section        0  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08002094   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08002104   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08002108   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002374   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080023d8   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08002668   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08002684   Section        0  uart_app.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x080026b4   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit_DMA                  0x080026b8   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    i.HAL_UART_Transmit_IT                   0x08002730   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT)
    i.HAL_UART_TxCpltCallback                0x08002764   Section        0  uart_app.o(i.HAL_UART_TxCpltCallback)
    i.HAL_UART_TxHalfCpltCallback            0x0800277c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    i.HardFault_Handler                      0x0800277e   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.Initial                                0x08002780   Section        0  initial.o(i.Initial)
    i.MX_DMA_Init                            0x080027c0   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x0800283c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM2_Init                           0x08002878   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x080028f8   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_USART1_UART_Init                    0x08002960   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08002998   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x080029d0   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x08002a08   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.Motor_Init                             0x08002a0c   Section        0  motor_function.o(i.Motor_Init)
    i.MyDataUpdateCallback                   0x08002a40   Section        0  wit_c_sdk.o(i.MyDataUpdateCallback)
    i.NMI_Handler                            0x08002a94   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.PID_Update                             0x08002a96   Section        0  color.o(i.PID_Update)
    i.PendSV_Handler                         0x08002b58   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.STM32_Delayms                          0x08002b5a   Section        0  wit_c_sdk.o(i.STM32_Delayms)
    i.STM32_SerialWrite                      0x08002b60   Section        0  wit_c_sdk.o(i.STM32_SerialWrite)
    i.SVC_Handler                            0x08002b98   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08002b9a   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08002b9e   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08002bfc   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM3_IRQHandler                        0x08002c00   Section        0  stm32f1xx_it.o(i.TIM3_IRQHandler)
    i.TIM_Base_SetConfig                     0x08002c0c   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08002c84   Section        0  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x08002c9e   Section        0  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08002cb2   Section        0  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08002cb3   Thumb Code    16  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08002cc4   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08002cc5   Thumb Code    74  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08002d14   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08002d6c   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08002d6d   Thumb Code    82  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08002dc4   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08002dc5   Thumb Code    64  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08002e08   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08002e09   Thumb Code    34  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08002e2a   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08002e2b   Thumb Code    36  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.Throw_garbageX                         0x08002e50   Section        0  color.o(i.Throw_garbageX)
    Throw_garbageX                           0x08002e51   Thumb Code    96  color.o(i.Throw_garbageX)
    i.Throw_garbageY                         0x08002ec0   Section        0  color.o(i.Throw_garbageY)
    Throw_garbageY                           0x08002ec1   Thumb Code    96  color.o(i.Throw_garbageY)
    i.UART_DMAAbortOnError                   0x08002f30   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08002f31   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08002f40   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08002f41   Thumb Code    74  stm32f1xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08002f8a   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08002f8b   Thumb Code   134  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08003010   Section        0  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08003011   Thumb Code    30  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_DMATransmitCplt                   0x0800302e   Section        0  stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt)
    UART_DMATransmitCplt                     0x0800302f   Thumb Code    66  stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt)
    i.UART_DMATxHalfCplt                     0x08003070   Section        0  stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt)
    UART_DMATxHalfCplt                       0x08003071   Thumb Code    10  stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt)
    i.UART_EndRxTransfer                     0x0800307a   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x0800307b   Thumb Code    78  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x080030c8   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x080030c9   Thumb Code    28  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x080030e4   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x080030e5   Thumb Code   194  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080031a8   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080031a9   Thumb Code   178  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08003260   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x080032f0   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.USART1_IRQHandler                      0x08003328   Section        0  stm32f1xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08003334   Section        0  stm32f1xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08003340   Section        0  stm32f1xx_it.o(i.USART3_IRQHandler)
    i.USART3_Start_Receive_IT                0x0800334c   Section        0  wit_c_sdk.o(i.USART3_Start_Receive_IT)
    i.UsageFault_Handler                     0x08003360   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.WitCaliRefAngle                        0x08003364   Section        0  wit_c_sdk.o(i.WitCaliRefAngle)
    i.WitDelayMsRegister                     0x080033a8   Section        0  wit_c_sdk.o(i.WitDelayMsRegister)
    i.WitInit                                0x080033bc   Section        0  wit_c_sdk.o(i.WitInit)
    i.WitRegisterCallBack                    0x080033d8   Section        0  wit_c_sdk.o(i.WitRegisterCallBack)
    i.WitSdk_Init                            0x080033ec   Section        0  wit_c_sdk.o(i.WitSdk_Init)
    i.WitSerialDataIn                        0x08003428   Section        0  wit_c_sdk.o(i.WitSerialDataIn)
    i.WitSerialWriteRegister                 0x08003528   Section        0  wit_c_sdk.o(i.WitSerialWriteRegister)
    i.WitWriteReg                            0x0800353c   Section        0  wit_c_sdk.o(i.WitWriteReg)
    i.Z_Motor_MoveToAngle                    0x08003628   Section        0  motor_function.o(i.Z_Motor_MoveToAngle)
    i.__0vsnprintf                           0x0800369c   Section        0  printfa.o(i.__0vsnprintf)
    i.__CRC16                                0x080036c8   Section        0  wit_c_sdk.o(i.__CRC16)
    __CRC16                                  0x080036c9   Thumb Code    38  wit_c_sdk.o(i.__CRC16)
    i.__NVIC_SetPriority                     0x080036f4   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080036f5   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__aeabi_errno_addr                     0x08003714   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__scatterload_copy                     0x0800371c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800372a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800372c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x0800373c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0800373d   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080038c0   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080038c1   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08003f9c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08003f9d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08003fc0   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08003fc1   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08003fee   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08003fef   Thumb Code    22  printfa.o(i._snputc)
    i.center_point                           0x08004004   Section        0  color.o(i.center_point)
    i.main                                   0x08004190   Section        0  main.o(i.main)
    i.my_printf                              0x080041f0   Section        0  uart_app.o(i.my_printf)
    i.yuntai_init                            0x08004220   Section        0  yuntai.o(i.yuntai_init)
    .constdata                               0x08004244   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x08004244   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x08004246   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x08004256   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x08004266   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x0800426e   Section      512  wit_c_sdk.o(.constdata)
    __auchCRCHi                              0x0800426e   Data         256  wit_c_sdk.o(.constdata)
    __auchCRCLo                              0x0800436e   Data         256  wit_c_sdk.o(.constdata)
    .constdata                               0x0800446e   Section      129  ctype_o.o(.constdata)
    .constdata                               0x080044f0   Section        4  ctype_o.o(.constdata)
    table                                    0x080044f0   Data           4  ctype_o.o(.constdata)
    .data                                    0x20000000   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000010   Section       46  uart_app.o(.data)
    .data                                    0x20000040   Section      232  color.o(.data)
    count_11                                 0x2000004a   Data           2  color.o(.data)
    .data                                    0x20000128   Section        8  yuntai.o(.data)
    .data                                    0x20000130   Section       48  wit_c_sdk.o(.data)
    s_ucAddr                                 0x20000131   Data           1  wit_c_sdk.o(.data)
    p_WitSerialWriteFunc                     0x20000138   Data           4  wit_c_sdk.o(.data)
    p_WitI2cWriteFunc                        0x2000013c   Data           4  wit_c_sdk.o(.data)
    p_WitI2cReadFunc                         0x20000140   Data           4  wit_c_sdk.o(.data)
    p_WitCanWriteFunc                        0x20000144   Data           4  wit_c_sdk.o(.data)
    p_WitRegUpdateCbFunc                     0x20000148   Data           4  wit_c_sdk.o(.data)
    p_WitDelaymsFunc                         0x2000014c   Data           4  wit_c_sdk.o(.data)
    s_uiWitDataCnt                           0x20000150   Data           4  wit_c_sdk.o(.data)
    s_uiProtoclo                             0x20000154   Data           4  wit_c_sdk.o(.data)
    s_uiReadRegIndex                         0x20000158   Data           4  wit_c_sdk.o(.data)
    .data                                    0x20000160   Section        4  motor_function.o(.data)
    z_current_angle                          0x20000160   Data           4  motor_function.o(.data)
    .data                                    0x20000164   Section        4  strtok.o(.data)
    state                                    0x20000164   Data           4  strtok.o(.data)
    .data                                    0x20000168   Section        4  errno.o(.data)
    _errno                                   0x20000168   Data           4  errno.o(.data)
    .bss                                     0x2000016c   Section      144  tim.o(.bss)
    .bss                                     0x200001fc   Section      624  usart.o(.bss)
    .bss                                     0x2000046c   Section      256  uart_app.o(.bss)
    .bss                                     0x2000056c   Section     1312  wit_c_sdk.o(.bss)
    s_ucWitDataBuff                          0x2000056c   Data        1024  wit_c_sdk.o(.bss)
    STACK                                    0x20000a90   Section     1024  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080000fd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080000fd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000101   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f103xb.o(.text)
    __aeabi_llsr                             0x08000125   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000125   Thumb Code     0  llushr.o(.text)
    __aeabi_memcpy                           0x08000145   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000145   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000145   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000169   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000169   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000169   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000177   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000177   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000177   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800017b   Thumb Code    18  memseta.o(.text)
    strtok                                   0x0800018d   Thumb Code    62  strtok.o(.text)
    atoi                                     0x080001d1   Thumb Code    26  atoi.o(.text)
    __aeabi_fadd                             0x080001eb   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x0800028f   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08000295   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x0800029b   Thumb Code   100  fmul.o(.text)
    __ARM_scalbnf                            0x080002ff   Thumb Code    24  fscalb.o(.text)
    scalbnf                                  0x080002ff   Thumb Code     0  fscalb.o(.text)
    __aeabi_dadd                             0x08000317   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000459   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800045f   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000465   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000549   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2f                              0x08000627   Thumb Code    18  fflti.o(.text)
    __aeabi_ui2d                             0x08000639   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2uiz                            0x08000653   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x08000685   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x080006ad   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x080006ad   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x080006dd   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x0800070d   Thumb Code    56  d2f.o(.text)
    __aeabi_cfcmpeq                          0x08000745   Thumb Code     0  cfcmple.o(.text)
    __aeabi_cfcmple                          0x08000745   Thumb Code    20  cfcmple.o(.text)
    __aeabi_cfrcmple                         0x08000759   Thumb Code    20  cfrcmple.o(.text)
    __aeabi_uidiv                            0x0800076d   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800076d   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08000799   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x080007fb   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080007fb   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x08000819   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000819   Thumb Code     0  llsshr.o(.text)
    strtol                                   0x0800083d   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x080008ad   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x080008ad   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080008bf   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800091b   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000939   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x080009d5   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08000a05   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000a05   Thumb Code     0  init.o(.text)
    __rt_ctype_table                         0x08000a29   Thumb Code     4  ctype_o.o(.text)
    _strtoul                                 0x08000a31   Thumb Code   158  _strtoul.o(.text)
    _chval                                   0x08000acf   Thumb Code    28  _chval.o(.text)
    __decompress                             0x08000aeb   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000aeb   Thumb Code    86  __dczerorl2.o(.text)
    BusFault_Handler                         0x08000b41   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    DMA1_Channel2_IRQHandler                 0x08000bf9   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel2_IRQHandler)
    DMA1_Channel3_IRQHandler                 0x08000c05   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler)
    DMA1_Channel4_IRQHandler                 0x08000c11   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel4_IRQHandler)
    DMA1_Channel5_IRQHandler                 0x08000c1d   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    DMA1_Channel6_IRQHandler                 0x08000c29   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler)
    DMA1_Channel7_IRQHandler                 0x08000c35   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel7_IRQHandler)
    DebugMon_Handler                         0x08000c6b   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Emm_V5_En_Control                        0x08000c6d   Thumb Code    54  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_Modify_Ctrl_Mode                  0x08000ca9   Thumb Code    54  emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode)
    Emm_V5_Pos_Control                       0x08000ce5   Thumb Code    98  emm_v5.o(i.Emm_V5_Pos_Control)
    Error_Handler                            0x08000d4d   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08000d51   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000d99   Thumb Code   148  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08000e31   Thumb Code   316  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08000f85   Thumb Code    84  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08000fe1   Thumb Code   112  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08001051   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08001075   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GetTick                              0x08001255   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08001261   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001271   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001295   Thumb Code    54  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080012d5   Thumb Code    52  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001311   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x0800132d   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800136d   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001391   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080014bd   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080014dd   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080014fd   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001549   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001869   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08001891   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08001893   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08001895   Thumb Code    92  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080018fd   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001959   Thumb Code    50  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08001995   Thumb Code    76  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x080019ed   Thumb Code   220  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_IC_CaptureCallback               0x08001ac9   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08001acb   Thumb Code   304  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08001bfd   Thumb Code    62  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08001c45   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08001c47   Thumb Code   204  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08001d13   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08001d6d   Thumb Code    30  tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08001d91   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08001d95   Thumb Code   144  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08001e31   Thumb Code    32  color.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08001e59   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08001e5b   Thumb Code    74  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08001ea5   Thumb Code   396  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08002095   Thumb Code   112  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08002105   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002109   Thumb Code   616  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002375   Thumb Code   100  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080023d9   Thumb Code   620  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08002669   Thumb Code    28  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08002685   Thumb Code    34  uart_app.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x080026b5   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit_DMA                    0x080026b9   Thumb Code   106  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    HAL_UART_Transmit_IT                     0x08002731   Thumb Code    50  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT)
    HAL_UART_TxCpltCallback                  0x08002765   Thumb Code    16  uart_app.o(i.HAL_UART_TxCpltCallback)
    HAL_UART_TxHalfCpltCallback              0x0800277d   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    HardFault_Handler                        0x0800277f   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    Initial                                  0x08002781   Thumb Code    46  initial.o(i.Initial)
    MX_DMA_Init                              0x080027c1   Thumb Code   120  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x0800283d   Thumb Code    54  gpio.o(i.MX_GPIO_Init)
    MX_TIM2_Init                             0x08002879   Thumb Code   122  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x080028f9   Thumb Code    94  tim.o(i.MX_TIM3_Init)
    MX_USART1_UART_Init                      0x08002961   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08002999   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x080029d1   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x08002a09   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    Motor_Init                               0x08002a0d   Thumb Code    48  motor_function.o(i.Motor_Init)
    MyDataUpdateCallback                     0x08002a41   Thumb Code    64  wit_c_sdk.o(i.MyDataUpdateCallback)
    NMI_Handler                              0x08002a95   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    PID_Update                               0x08002a97   Thumb Code   194  color.o(i.PID_Update)
    PendSV_Handler                           0x08002b59   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    STM32_Delayms                            0x08002b5b   Thumb Code     4  wit_c_sdk.o(i.STM32_Delayms)
    STM32_SerialWrite                        0x08002b61   Thumb Code    46  wit_c_sdk.o(i.STM32_SerialWrite)
    SVC_Handler                              0x08002b99   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08002b9b   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08002b9f   Thumb Code    94  main.o(i.SystemClock_Config)
    SystemInit                               0x08002bfd   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM3_IRQHandler                          0x08002c01   Thumb Code     6  stm32f1xx_it.o(i.TIM3_IRQHandler)
    TIM_Base_SetConfig                       0x08002c0d   Thumb Code   108  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08002c85   Thumb Code    26  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08002c9f   Thumb Code    20  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08002d15   Thumb Code    84  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_Start_Receive_DMA                   0x08003261   Thumb Code   130  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x080032f1   Thumb Code    54  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08003329   Thumb Code     6  stm32f1xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08003335   Thumb Code     6  stm32f1xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08003341   Thumb Code     6  stm32f1xx_it.o(i.USART3_IRQHandler)
    USART3_Start_Receive_IT                  0x0800334d   Thumb Code    10  wit_c_sdk.o(i.USART3_Start_Receive_IT)
    UsageFault_Handler                       0x08003361   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    WitCaliRefAngle                          0x08003365   Thumb Code    64  wit_c_sdk.o(i.WitCaliRefAngle)
    WitDelayMsRegister                       0x080033a9   Thumb Code    16  wit_c_sdk.o(i.WitDelayMsRegister)
    WitInit                                  0x080033bd   Thumb Code    22  wit_c_sdk.o(i.WitInit)
    WitRegisterCallBack                      0x080033d9   Thumb Code    16  wit_c_sdk.o(i.WitRegisterCallBack)
    WitSdk_Init                              0x080033ed   Thumb Code    48  wit_c_sdk.o(i.WitSdk_Init)
    WitSerialDataIn                          0x08003429   Thumb Code   244  wit_c_sdk.o(i.WitSerialDataIn)
    WitSerialWriteRegister                   0x08003529   Thumb Code    16  wit_c_sdk.o(i.WitSerialWriteRegister)
    WitWriteReg                              0x0800353d   Thumb Code   232  wit_c_sdk.o(i.WitWriteReg)
    Z_Motor_MoveToAngle                      0x08003629   Thumb Code    94  motor_function.o(i.Z_Motor_MoveToAngle)
    __0vsnprintf                             0x0800369d   Thumb Code    40  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x0800369d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x0800369d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x0800369d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x0800369d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __aeabi_errno_addr                       0x08003715   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x08003715   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __scatterload_copy                       0x0800371d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800372b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800372d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    center_point                             0x08004005   Thumb Code   302  color.o(i.center_point)
    main                                     0x08004191   Thumb Code    70  main.o(i.main)
    my_printf                                0x080041f1   Thumb Code    48  uart_app.o(i.my_printf)
    yuntai_init                              0x08004221   Thumb Code    28  yuntai.o(i.yuntai_init)
    AHBPrescTable                            0x08004256   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x08004266   Data           8  system_stm32f1xx.o(.constdata)
    __ctype_table                            0x0800446e   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x080044f4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08004514   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f1xx.o(.data)
    square_flag                              0x20000010   Data           1  uart_app.o(.data)
    red_flag                                 0x20000011   Data           1  uart_app.o(.data)
    circle_flag                              0x20000012   Data           1  uart_app.o(.data)
    Lx1                                      0x20000014   Data           2  uart_app.o(.data)
    Ly1                                      0x20000016   Data           2  uart_app.o(.data)
    Lx2                                      0x20000018   Data           2  uart_app.o(.data)
    Ly2                                      0x2000001a   Data           2  uart_app.o(.data)
    Lx3                                      0x2000001c   Data           2  uart_app.o(.data)
    Ly3                                      0x2000001e   Data           2  uart_app.o(.data)
    Lx4                                      0x20000020   Data           2  uart_app.o(.data)
    Ly4                                      0x20000022   Data           2  uart_app.o(.data)
    PX                                       0x20000024   Data           2  uart_app.o(.data)
    PY                                       0x20000026   Data           2  uart_app.o(.data)
    CircleX                                  0x20000028   Data           2  uart_app.o(.data)
    CircleY                                  0x2000002a   Data           2  uart_app.o(.data)
    Radius                                   0x2000002c   Data           2  uart_app.o(.data)
    CX1                                      0x2000002e   Data           2  uart_app.o(.data)
    CY1                                      0x20000030   Data           2  uart_app.o(.data)
    CX2                                      0x20000032   Data           2  uart_app.o(.data)
    CY2                                      0x20000034   Data           2  uart_app.o(.data)
    CX3                                      0x20000036   Data           2  uart_app.o(.data)
    CY3                                      0x20000038   Data           2  uart_app.o(.data)
    CX4                                      0x2000003a   Data           2  uart_app.o(.data)
    CY4                                      0x2000003c   Data           2  uart_app.o(.data)
    star_point_num                           0x20000040   Data           1  color.o(.data)
    point_num                                0x20000041   Data           1  color.o(.data)
    pid_flag1                                0x20000042   Data           1  color.o(.data)
    pid_flag2                                0x20000043   Data           1  color.o(.data)
    pid_flag3                                0x20000044   Data           1  color.o(.data)
    pid_flag4                                0x20000045   Data           1  color.o(.data)
    segment                                  0x20000046   Data           1  color.o(.data)
    sub_point                                0x20000047   Data           1  color.o(.data)
    point_id                                 0x20000048   Data           1  color.o(.data)
    P_Angle                                  0x20000050   Data           8  color.o(.data)
    Y_Angle                                  0x20000058   Data           8  color.o(.data)
    x_error                                  0x20000060   Data           8  color.o(.data)
    y_error                                  0x20000068   Data           8  color.o(.data)
    x_error1                                 0x20000070   Data           8  color.o(.data)
    y_error1                                 0x20000078   Data           8  color.o(.data)
    square_X                                 0x20000080   Data           8  color.o(.data)
    square_Y                                 0x20000088   Data           8  color.o(.data)
    K1                                       0x20000090   Data           8  color.o(.data)
    K2                                       0x20000098   Data           8  color.o(.data)
    angle                                    0x200000a0   Data           8  color.o(.data)
    targetX                                  0x200000a8   Data           8  color.o(.data)
    targetY                                  0x200000b0   Data           8  color.o(.data)
    pid_z_angle                              0x200000b8   Data          56  color.o(.data)
    pid_y_angle                              0x200000f0   Data          56  color.o(.data)
    Y_yuntai                                 0x20000128   Data           8  yuntai.o(.data)
    y901s_tx_busy                            0x20000130   Data           1  wit_c_sdk.o(.data)
    rx_byte                                  0x20000132   Data           1  wit_c_sdk.o(.data)
    z_avg_error                              0x20000134   Data           4  wit_c_sdk.o(.data)
    Z_Angle                                  0x2000015c   Data           4  wit_c_sdk.o(.data)
    htim2                                    0x2000016c   Data          72  tim.o(.bss)
    htim3                                    0x200001b4   Data          72  tim.o(.bss)
    huart1                                   0x200001fc   Data          72  usart.o(.bss)
    huart2                                   0x20000244   Data          72  usart.o(.bss)
    huart3                                   0x2000028c   Data          72  usart.o(.bss)
    hdma_usart1_tx                           0x200002d4   Data          68  usart.o(.bss)
    hdma_usart1_rx                           0x20000318   Data          68  usart.o(.bss)
    hdma_usart2_rx                           0x2000035c   Data          68  usart.o(.bss)
    hdma_usart2_tx                           0x200003a0   Data          68  usart.o(.bss)
    hdma_usart3_tx                           0x200003e4   Data          68  usart.o(.bss)
    hdma_usart3_rx                           0x20000428   Data          68  usart.o(.bss)
    uart_dma_buffer                          0x2000046c   Data         128  uart_app.o(.bss)
    uart_rx_dma_buffer                       0x200004ec   Data         128  uart_app.o(.bss)
    sReg                                     0x2000096c   Data         288  wit_c_sdk.o(.bss)
    __initial_sp                             0x20000e90   Data           0  startup_stm32f103xb.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00004680, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x00004548])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00004514, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x080000ec   0x00000000   Code   RO         3525  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x080000ec   0x00000004   Code   RO         3632    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x080000f0   0x00000004   Code   RO         3635    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         3637    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         3639    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x080000f4   0x00000008   Code   RO         3640    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         3642    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         3644    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080000fc   0x080000fc   0x00000004   Code   RO         3633    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000100   0x08000100   0x00000024   Code   RO            4    .text               startup_stm32f103xb.o
    0x08000124   0x08000124   0x00000020   Code   RO         3528    .text               mc_w.l(llushr.o)
    0x08000144   0x08000144   0x00000024   Code   RO         3530    .text               mc_w.l(memcpya.o)
    0x08000168   0x08000168   0x00000024   Code   RO         3532    .text               mc_w.l(memseta.o)
    0x0800018c   0x0800018c   0x00000044   Code   RO         3534    .text               mc_w.l(strtok.o)
    0x080001d0   0x080001d0   0x0000001a   Code   RO         3570    .text               mc_w.l(atoi.o)
    0x080001ea   0x080001ea   0x000000b0   Code   RO         3572    .text               mf_w.l(fadd.o)
    0x0800029a   0x0800029a   0x00000064   Code   RO         3574    .text               mf_w.l(fmul.o)
    0x080002fe   0x080002fe   0x00000018   Code   RO         3576    .text               mf_w.l(fscalb.o)
    0x08000316   0x08000316   0x0000014e   Code   RO         3578    .text               mf_w.l(dadd.o)
    0x08000464   0x08000464   0x000000e4   Code   RO         3580    .text               mf_w.l(dmul.o)
    0x08000548   0x08000548   0x000000de   Code   RO         3582    .text               mf_w.l(ddiv.o)
    0x08000626   0x08000626   0x00000012   Code   RO         3584    .text               mf_w.l(fflti.o)
    0x08000638   0x08000638   0x0000001a   Code   RO         3588    .text               mf_w.l(dfltui.o)
    0x08000652   0x08000652   0x00000032   Code   RO         3590    .text               mf_w.l(dfixui.o)
    0x08000684   0x08000684   0x00000026   Code   RO         3592    .text               mf_w.l(f2d.o)
    0x080006aa   0x080006aa   0x00000002   PAD
    0x080006ac   0x080006ac   0x00000030   Code   RO         3594    .text               mf_w.l(cdcmple.o)
    0x080006dc   0x080006dc   0x00000030   Code   RO         3596    .text               mf_w.l(cdrcmple.o)
    0x0800070c   0x0800070c   0x00000038   Code   RO         3598    .text               mf_w.l(d2f.o)
    0x08000744   0x08000744   0x00000014   Code   RO         3600    .text               mf_w.l(cfcmple.o)
    0x08000758   0x08000758   0x00000014   Code   RO         3602    .text               mf_w.l(cfrcmple.o)
    0x0800076c   0x0800076c   0x0000002c   Code   RO         3649    .text               mc_w.l(uidiv.o)
    0x08000798   0x08000798   0x00000062   Code   RO         3651    .text               mc_w.l(uldiv.o)
    0x080007fa   0x080007fa   0x0000001e   Code   RO         3653    .text               mc_w.l(llshl.o)
    0x08000818   0x08000818   0x00000024   Code   RO         3655    .text               mc_w.l(llsshr.o)
    0x0800083c   0x0800083c   0x00000070   Code   RO         3664    .text               mc_w.l(strtol.o)
    0x080008ac   0x080008ac   0x00000000   Code   RO         3666    .text               mc_w.l(iusefp.o)
    0x080008ac   0x080008ac   0x0000006e   Code   RO         3667    .text               mf_w.l(fepilogue.o)
    0x0800091a   0x0800091a   0x000000ba   Code   RO         3669    .text               mf_w.l(depilogue.o)
    0x080009d4   0x080009d4   0x00000030   Code   RO         3671    .text               mf_w.l(dfixul.o)
    0x08000a04   0x08000a04   0x00000024   Code   RO         3677    .text               mc_w.l(init.o)
    0x08000a28   0x08000a28   0x00000008   Code   RO         3680    .text               mc_w.l(ctype_o.o)
    0x08000a30   0x08000a30   0x0000009e   Code   RO         3708    .text               mc_w.l(_strtoul.o)
    0x08000ace   0x08000ace   0x0000001c   Code   RO         3717    .text               mc_w.l(_chval.o)
    0x08000aea   0x08000aea   0x00000056   Code   RO         3727    .text               mc_w.l(__dczerorl2.o)
    0x08000b40   0x08000b40   0x00000002   Code   RO          371    i.BusFault_Handler  stm32f1xx_it.o
    0x08000b42   0x08000b42   0x00000002   PAD
    0x08000b44   0x08000b44   0x000000b4   Code   RO         3152    i.CopeWitData       wit_c_sdk.o
    0x08000bf8   0x08000bf8   0x0000000c   Code   RO          372    i.DMA1_Channel2_IRQHandler  stm32f1xx_it.o
    0x08000c04   0x08000c04   0x0000000c   Code   RO          373    i.DMA1_Channel3_IRQHandler  stm32f1xx_it.o
    0x08000c10   0x08000c10   0x0000000c   Code   RO          374    i.DMA1_Channel4_IRQHandler  stm32f1xx_it.o
    0x08000c1c   0x08000c1c   0x0000000c   Code   RO          375    i.DMA1_Channel5_IRQHandler  stm32f1xx_it.o
    0x08000c28   0x08000c28   0x0000000c   Code   RO          376    i.DMA1_Channel6_IRQHandler  stm32f1xx_it.o
    0x08000c34   0x08000c34   0x0000000c   Code   RO          377    i.DMA1_Channel7_IRQHandler  stm32f1xx_it.o
    0x08000c40   0x08000c40   0x0000002a   Code   RO         1920    i.DMA_SetConfig     stm32f1xx_hal_dma.o
    0x08000c6a   0x08000c6a   0x00000002   Code   RO          378    i.DebugMon_Handler  stm32f1xx_it.o
    0x08000c6c   0x08000c6c   0x0000003c   Code   RO         3360    i.Emm_V5_En_Control  emm_v5.o
    0x08000ca8   0x08000ca8   0x0000003c   Code   RO         3361    i.Emm_V5_Modify_Ctrl_Mode  emm_v5.o
    0x08000ce4   0x08000ce4   0x00000068   Code   RO         3366    i.Emm_V5_Pos_Control  emm_v5.o
    0x08000d4c   0x08000d4c   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000d50   0x08000d50   0x00000046   Code   RO         1921    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x08000d96   0x08000d96   0x00000002   PAD
    0x08000d98   0x08000d98   0x00000098   Code   RO         1922    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08000e30   0x08000e30   0x00000154   Code   RO         1926    i.HAL_DMA_IRQHandler  stm32f1xx_hal_dma.o
    0x08000f84   0x08000f84   0x0000005c   Code   RO         1927    i.HAL_DMA_Init      stm32f1xx_hal_dma.o
    0x08000fe0   0x08000fe0   0x00000070   Code   RO         1931    i.HAL_DMA_Start_IT  stm32f1xx_hal_dma.o
    0x08001050   0x08001050   0x00000024   Code   RO         1547    i.HAL_Delay         stm32f1xx_hal.o
    0x08001074   0x08001074   0x000001e0   Code   RO         1857    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08001254   0x08001254   0x0000000c   Code   RO         1551    i.HAL_GetTick       stm32f1xx_hal.o
    0x08001260   0x08001260   0x00000010   Code   RO         1557    i.HAL_IncTick       stm32f1xx_hal.o
    0x08001270   0x08001270   0x00000024   Code   RO         1558    i.HAL_Init          stm32f1xx_hal.o
    0x08001294   0x08001294   0x00000040   Code   RO         1559    i.HAL_InitTick      stm32f1xx_hal.o
    0x080012d4   0x080012d4   0x0000003c   Code   RO          507    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08001310   0x08001310   0x0000001a   Code   RO         2017    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x0800132a   0x0800132a   0x00000002   PAD
    0x0800132c   0x0800132c   0x00000040   Code   RO         2023    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x0800136c   0x0800136c   0x00000024   Code   RO         2024    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08001390   0x08001390   0x0000012c   Code   RO         1715    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x080014bc   0x080014bc   0x00000020   Code   RO         1722    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x080014dc   0x080014dc   0x00000020   Code   RO         1723    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x080014fc   0x080014fc   0x0000004c   Code   RO         1724    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08001548   0x08001548   0x00000320   Code   RO         1727    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08001868   0x08001868   0x00000028   Code   RO         2028    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08001890   0x08001890   0x00000002   Code   RO         1273    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x08001892   0x08001892   0x00000002   Code   RO         1274    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x08001894   0x08001894   0x00000068   Code   RO         1292    i.HAL_TIMEx_MasterConfigSynchronization  stm32f1xx_hal_tim_ex.o
    0x080018fc   0x080018fc   0x0000005a   Code   RO          569    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x08001956   0x08001956   0x00000002   PAD
    0x08001958   0x08001958   0x0000003c   Code   RO          252    i.HAL_TIM_Base_MspInit  tim.o
    0x08001994   0x08001994   0x00000058   Code   RO          574    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x080019ec   0x080019ec   0x000000dc   Code   RO          578    i.HAL_TIM_ConfigClockSource  stm32f1xx_hal_tim.o
    0x08001ac8   0x08001ac8   0x00000002   Code   RO          603    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x08001aca   0x08001aca   0x00000130   Code   RO          617    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x08001bfa   0x08001bfa   0x00000002   PAD
    0x08001bfc   0x08001bfc   0x00000048   Code   RO          253    i.HAL_TIM_MspPostInit  tim.o
    0x08001c44   0x08001c44   0x00000002   Code   RO          620    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x08001c46   0x08001c46   0x000000cc   Code   RO          641    i.HAL_TIM_PWM_ConfigChannel  stm32f1xx_hal_tim.o
    0x08001d12   0x08001d12   0x0000005a   Code   RO          644    i.HAL_TIM_PWM_Init  stm32f1xx_hal_tim.o
    0x08001d6c   0x08001d6c   0x00000024   Code   RO          255    i.HAL_TIM_PWM_MspInit  tim.o
    0x08001d90   0x08001d90   0x00000002   Code   RO          647    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x08001d92   0x08001d92   0x00000002   PAD
    0x08001d94   0x08001d94   0x0000009c   Code   RO          649    i.HAL_TIM_PWM_Start  stm32f1xx_hal_tim.o
    0x08001e30   0x08001e30   0x00000028   Code   RO         2992    i.HAL_TIM_PeriodElapsedCallback  color.o
    0x08001e58   0x08001e58   0x00000002   Code   RO          660    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x08001e5a   0x08001e5a   0x0000004a   Code   RO         2527    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f1xx_hal_uart.o
    0x08001ea4   0x08001ea4   0x000001f0   Code   RO         2918    i.HAL_UARTEx_RxEventCallback  uart_app.o
    0x08002094   0x08002094   0x00000070   Code   RO         2541    i.HAL_UART_DMAStop  stm32f1xx_hal_uart.o
    0x08002104   0x08002104   0x00000002   Code   RO         2543    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x08002106   0x08002106   0x00000002   PAD
    0x08002108   0x08002108   0x0000026c   Code   RO         2546    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x08002374   0x08002374   0x00000064   Code   RO         2547    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x080023d8   0x080023d8   0x00000290   Code   RO          318    i.HAL_UART_MspInit  usart.o
    0x08002668   0x08002668   0x0000001c   Code   RO         2552    i.HAL_UART_Receive_IT  stm32f1xx_hal_uart.o
    0x08002684   0x08002684   0x00000030   Code   RO         2919    i.HAL_UART_RxCpltCallback  uart_app.o
    0x080026b4   0x080026b4   0x00000002   Code   RO         2554    i.HAL_UART_RxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x080026b6   0x080026b6   0x00000002   PAD
    0x080026b8   0x080026b8   0x00000078   Code   RO         2556    i.HAL_UART_Transmit_DMA  stm32f1xx_hal_uart.o
    0x08002730   0x08002730   0x00000032   Code   RO         2557    i.HAL_UART_Transmit_IT  stm32f1xx_hal_uart.o
    0x08002762   0x08002762   0x00000002   PAD
    0x08002764   0x08002764   0x00000018   Code   RO         2920    i.HAL_UART_TxCpltCallback  uart_app.o
    0x0800277c   0x0800277c   0x00000002   Code   RO         2559    i.HAL_UART_TxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x0800277e   0x0800277e   0x00000002   Code   RO          379    i.HardFault_Handler  stm32f1xx_it.o
    0x08002780   0x08002780   0x00000040   Code   RO         3127    i.Initial           initial.o
    0x080027c0   0x080027c0   0x0000007c   Code   RO          227    i.MX_DMA_Init       dma.o
    0x0800283c   0x0800283c   0x0000003c   Code   RO          203    i.MX_GPIO_Init      gpio.o
    0x08002878   0x08002878   0x00000080   Code   RO          256    i.MX_TIM2_Init      tim.o
    0x080028f8   0x080028f8   0x00000068   Code   RO          257    i.MX_TIM3_Init      tim.o
    0x08002960   0x08002960   0x00000038   Code   RO          319    i.MX_USART1_UART_Init  usart.o
    0x08002998   0x08002998   0x00000038   Code   RO          320    i.MX_USART2_UART_Init  usart.o
    0x080029d0   0x080029d0   0x00000038   Code   RO          321    i.MX_USART3_UART_Init  usart.o
    0x08002a08   0x08002a08   0x00000002   Code   RO          380    i.MemManage_Handler  stm32f1xx_it.o
    0x08002a0a   0x08002a0a   0x00000002   PAD
    0x08002a0c   0x08002a0c   0x00000034   Code   RO         3456    i.Motor_Init        motor_function.o
    0x08002a40   0x08002a40   0x00000054   Code   RO         3153    i.MyDataUpdateCallback  wit_c_sdk.o
    0x08002a94   0x08002a94   0x00000002   Code   RO          381    i.NMI_Handler       stm32f1xx_it.o
    0x08002a96   0x08002a96   0x000000c2   Code   RO         2993    i.PID_Update        color.o
    0x08002b58   0x08002b58   0x00000002   Code   RO          382    i.PendSV_Handler    stm32f1xx_it.o
    0x08002b5a   0x08002b5a   0x00000004   Code   RO         3154    i.STM32_Delayms     wit_c_sdk.o
    0x08002b5e   0x08002b5e   0x00000002   PAD
    0x08002b60   0x08002b60   0x00000038   Code   RO         3155    i.STM32_SerialWrite  wit_c_sdk.o
    0x08002b98   0x08002b98   0x00000002   Code   RO          383    i.SVC_Handler       stm32f1xx_it.o
    0x08002b9a   0x08002b9a   0x00000004   Code   RO          384    i.SysTick_Handler   stm32f1xx_it.o
    0x08002b9e   0x08002b9e   0x0000005e   Code   RO           14    i.SystemClock_Config  main.o
    0x08002bfc   0x08002bfc   0x00000002   Code   RO         2881    i.SystemInit        system_stm32f1xx.o
    0x08002bfe   0x08002bfe   0x00000002   PAD
    0x08002c00   0x08002c00   0x0000000c   Code   RO          385    i.TIM3_IRQHandler   stm32f1xx_it.o
    0x08002c0c   0x08002c0c   0x00000078   Code   RO          662    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08002c84   0x08002c84   0x0000001a   Code   RO          663    i.TIM_CCxChannelCmd  stm32f1xx_hal_tim.o
    0x08002c9e   0x08002c9e   0x00000014   Code   RO          673    i.TIM_ETR_SetConfig  stm32f1xx_hal_tim.o
    0x08002cb2   0x08002cb2   0x00000010   Code   RO          674    i.TIM_ITRx_SetConfig  stm32f1xx_hal_tim.o
    0x08002cc2   0x08002cc2   0x00000002   PAD
    0x08002cc4   0x08002cc4   0x00000050   Code   RO          675    i.TIM_OC1_SetConfig  stm32f1xx_hal_tim.o
    0x08002d14   0x08002d14   0x00000058   Code   RO          676    i.TIM_OC2_SetConfig  stm32f1xx_hal_tim.o
    0x08002d6c   0x08002d6c   0x00000058   Code   RO          677    i.TIM_OC3_SetConfig  stm32f1xx_hal_tim.o
    0x08002dc4   0x08002dc4   0x00000044   Code   RO          678    i.TIM_OC4_SetConfig  stm32f1xx_hal_tim.o
    0x08002e08   0x08002e08   0x00000022   Code   RO          680    i.TIM_TI1_ConfigInputStage  stm32f1xx_hal_tim.o
    0x08002e2a   0x08002e2a   0x00000024   Code   RO          682    i.TIM_TI2_ConfigInputStage  stm32f1xx_hal_tim.o
    0x08002e4e   0x08002e4e   0x00000002   PAD
    0x08002e50   0x08002e50   0x00000070   Code   RO         2995    i.Throw_garbageX    color.o
    0x08002ec0   0x08002ec0   0x00000070   Code   RO         2996    i.Throw_garbageY    color.o
    0x08002f30   0x08002f30   0x00000010   Code   RO         2560    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x08002f40   0x08002f40   0x0000004a   Code   RO         2561    i.UART_DMAError     stm32f1xx_hal_uart.o
    0x08002f8a   0x08002f8a   0x00000086   Code   RO         2562    i.UART_DMAReceiveCplt  stm32f1xx_hal_uart.o
    0x08003010   0x08003010   0x0000001e   Code   RO         2564    i.UART_DMARxHalfCplt  stm32f1xx_hal_uart.o
    0x0800302e   0x0800302e   0x00000042   Code   RO         2566    i.UART_DMATransmitCplt  stm32f1xx_hal_uart.o
    0x08003070   0x08003070   0x0000000a   Code   RO         2568    i.UART_DMATxHalfCplt  stm32f1xx_hal_uart.o
    0x0800307a   0x0800307a   0x0000004e   Code   RO         2570    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x080030c8   0x080030c8   0x0000001c   Code   RO         2571    i.UART_EndTxTransfer  stm32f1xx_hal_uart.o
    0x080030e4   0x080030e4   0x000000c2   Code   RO         2572    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x080031a6   0x080031a6   0x00000002   PAD
    0x080031a8   0x080031a8   0x000000b8   Code   RO         2573    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x08003260   0x08003260   0x00000090   Code   RO         2574    i.UART_Start_Receive_DMA  stm32f1xx_hal_uart.o
    0x080032f0   0x080032f0   0x00000036   Code   RO         2575    i.UART_Start_Receive_IT  stm32f1xx_hal_uart.o
    0x08003326   0x08003326   0x00000002   PAD
    0x08003328   0x08003328   0x0000000c   Code   RO          386    i.USART1_IRQHandler  stm32f1xx_it.o
    0x08003334   0x08003334   0x0000000c   Code   RO          387    i.USART2_IRQHandler  stm32f1xx_it.o
    0x08003340   0x08003340   0x0000000c   Code   RO          388    i.USART3_IRQHandler  stm32f1xx_it.o
    0x0800334c   0x0800334c   0x00000014   Code   RO         3156    i.USART3_Start_Receive_IT  wit_c_sdk.o
    0x08003360   0x08003360   0x00000002   Code   RO          389    i.UsageFault_Handler  stm32f1xx_it.o
    0x08003362   0x08003362   0x00000002   PAD
    0x08003364   0x08003364   0x00000044   Code   RO         3157    i.WitCaliRefAngle   wit_c_sdk.o
    0x080033a8   0x080033a8   0x00000014   Code   RO         3161    i.WitDelayMsRegister  wit_c_sdk.o
    0x080033bc   0x080033bc   0x0000001c   Code   RO         3163    i.WitInit           wit_c_sdk.o
    0x080033d8   0x080033d8   0x00000014   Code   RO         3165    i.WitRegisterCallBack  wit_c_sdk.o
    0x080033ec   0x080033ec   0x0000003c   Code   RO         3167    i.WitSdk_Init       wit_c_sdk.o
    0x08003428   0x08003428   0x00000100   Code   RO         3168    i.WitSerialDataIn   wit_c_sdk.o
    0x08003528   0x08003528   0x00000014   Code   RO         3169    i.WitSerialWriteRegister  wit_c_sdk.o
    0x0800353c   0x0800353c   0x000000ec   Code   RO         3180    i.WitWriteReg       wit_c_sdk.o
    0x08003628   0x08003628   0x00000074   Code   RO         3458    i.Z_Motor_MoveToAngle  motor_function.o
    0x0800369c   0x0800369c   0x0000002c   Code   RO         3548    i.__0vsnprintf      mc_w.l(printfa.o)
    0x080036c8   0x080036c8   0x0000002c   Code   RO         3181    i.__CRC16           wit_c_sdk.o
    0x080036f4   0x080036f4   0x00000020   Code   RO         2030    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08003714   0x08003714   0x00000008   Code   RO         3657    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x0800371c   0x0800371c   0x0000000e   Code   RO         3721    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800372a   0x0800372a   0x00000002   Code   RO         3722    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800372c   0x0800372c   0x0000000e   Code   RO         3723    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800373a   0x0800373a   0x00000002   PAD
    0x0800373c   0x0800373c   0x00000184   Code   RO         3550    i._fp_digits        mc_w.l(printfa.o)
    0x080038c0   0x080038c0   0x000006dc   Code   RO         3551    i._printf_core      mc_w.l(printfa.o)
    0x08003f9c   0x08003f9c   0x00000024   Code   RO         3552    i._printf_post_padding  mc_w.l(printfa.o)
    0x08003fc0   0x08003fc0   0x0000002e   Code   RO         3553    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08003fee   0x08003fee   0x00000016   Code   RO         3554    i._snputc           mc_w.l(printfa.o)
    0x08004004   0x08004004   0x0000018c   Code   RO         2997    i.center_point      color.o
    0x08004190   0x08004190   0x00000060   Code   RO           15    i.main              main.o
    0x080041f0   0x080041f0   0x00000030   Code   RO         2921    i.my_printf         uart_app.o
    0x08004220   0x08004220   0x00000024   Code   RO         3086    i.yuntai_init       yuntai.o
    0x08004244   0x08004244   0x00000012   Data   RO         1728    .constdata          stm32f1xx_hal_rcc.o
    0x08004256   0x08004256   0x00000010   Data   RO         2882    .constdata          system_stm32f1xx.o
    0x08004266   0x08004266   0x00000008   Data   RO         2883    .constdata          system_stm32f1xx.o
    0x0800426e   0x0800426e   0x00000200   Data   RO         3183    .constdata          wit_c_sdk.o
    0x0800446e   0x0800446e   0x00000081   Data   RO         3681    .constdata          mc_w.l(ctype_o.o)
    0x080044ef   0x080044ef   0x00000001   PAD
    0x080044f0   0x080044f0   0x00000004   Data   RO         3682    .constdata          mc_w.l(ctype_o.o)
    0x080044f4   0x080044f4   0x00000020   Data   RO         3719    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08004514, Size: 0x00000e90, Max: 0x00005000, ABSOLUTE, COMPRESSED[0x00000034])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x0000000c   Data   RW         1565    .data               stm32f1xx_hal.o
    0x2000000c   COMPRESSED   0x00000004   Data   RW         2884    .data               system_stm32f1xx.o
    0x20000010   COMPRESSED   0x0000002e   Data   RW         2928    .data               uart_app.o
    0x2000003e   COMPRESSED   0x00000002   PAD
    0x20000040   COMPRESSED   0x000000e8   Data   RW         3000    .data               color.o
    0x20000128   COMPRESSED   0x00000008   Data   RW         3087    .data               yuntai.o
    0x20000130   COMPRESSED   0x00000030   Data   RW         3184    .data               wit_c_sdk.o
    0x20000160   COMPRESSED   0x00000004   Data   RW         3462    .data               motor_function.o
    0x20000164   COMPRESSED   0x00000004   Data   RW         3535    .data               mc_w.l(strtok.o)
    0x20000168   COMPRESSED   0x00000004   Data   RW         3660    .data               mc_w.l(errno.o)
    0x2000016c        -       0x00000090   Zero   RW          258    .bss                tim.o
    0x200001fc        -       0x00000270   Zero   RW          322    .bss                usart.o
    0x2000046c        -       0x00000100   Zero   RW         2922    .bss                uart_app.o
    0x2000056c        -       0x00000520   Zero   RW         3182    .bss                wit_c_sdk.o
    0x20000a8c   COMPRESSED   0x00000004   PAD
    0x20000a90        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       854        134          0        232          0       4874   color.o
       124          4          0          0          0        718   dma.o
       224         18          0          0          0       1807   emm_v5.o
        60          6          0          0          0        739   gpio.o
        64         18          0          0          0        422   initial.o
       194         26          0          0          0     427320   main.o
       168         26          0          4          0       1907   motor_function.o
        36          8        236          0       1024        764   startup_stm32f103xb.o
       164         28          0         12          0       5589   stm32f1xx_hal.o
       198         14          0          0          0      28735   stm32f1xx_hal_cortex.o
       808         36          0          0          0       4714   stm32f1xx_hal_dma.o
       480         34          0          0          0       2184   stm32f1xx_hal_gpio.o
        60          8          0          0          0        798   stm32f1xx_hal_msp.o
      1240         84         18          0          0       4876   stm32f1xx_hal_rcc.o
      1736         56          0          0          0      14740   stm32f1xx_hal_tim.o
       108         12          0          0          0       2245   stm32f1xx_hal_tim_ex.o
      2122         38          0          0          0      15615   stm32f1xx_hal_uart.o
       140         60          0          0          0       7562   stm32f1xx_it.o
         2          0         24          4          0        999   system_stm32f1xx.o
       400         42          0          0        144       3163   tim.o
       616        122          0         46        256       4706   uart_app.o
       824         60          0          0        624       3330   usart.o
      1096        136        512         48       1312      10699   wit_c_sdk.o
        36          8          0          8          0        618   yuntai.o

    ----------------------------------------------------------------------
     11788        <USER>        <GROUP>        356       3364     549124   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        34          0          0          2          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
        28          0          0          0          0         68   _chval.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
         8          4          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2292         84          0          0          0        516   printfa.o
        68          6          0          4          0         80   strtok.o
       112          0          0          0          0         88   strtol.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        20          0          0          0          0         68   cfcmple.o
        20          0          0          0          0         68   cfrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        48          0          0          0          0         68   dfixul.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       176          0          0          0          0        140   fadd.o
       110          0          0          0          0        168   fepilogue.o
        18          0          0          0          0         68   fflti.o
       100          0          0          0          0         76   fmul.o
        24          0          0          0          0         68   fscalb.o

    ----------------------------------------------------------------------
      4940        <USER>        <GROUP>          8          0       3368   Library Totals
         4          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3184        114        133          8          0       1680   mc_w.l
      1752          0          0          0          0       1688   mf_w.l

    ----------------------------------------------------------------------
      4940        <USER>        <GROUP>          8          0       3368   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     16728       1092        956        364       3364     544092   Grand Totals
     16728       1092        956         52       3364     544092   ELF Image Totals (compressed)
     16728       1092        956         52          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                17684 (  17.27kB)
    Total RW  Size (RW Data + ZI Data)              3728 (   3.64kB)
    Total ROM Size (Code + RO Data + RW Data)      17736 (  17.32kB)

==============================================================================

